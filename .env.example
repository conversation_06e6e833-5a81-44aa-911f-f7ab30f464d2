# Environment variables for ErzenAI Open

# -----------------------------------------------------------------
# Frontend Environment Variables (.env.local)
# -----------------------------------------------------------------
# These variables are used by the Vite frontend.
# Create a .env.local file in your project root and add these.

# Get this from the Convex dashboard or the output of `npx convex dev`.
VITE_CONVEX_URL="your-convex-deployment-url"


# -----------------------------------------------------------------
# Backend Environment Variables (Set in Convex Dashboard)
# -----------------------------------------------------------------
# These variables must be set in your Convex project's dashboard.
# Go to your project -> Settings -> Environment Variables.
# Do NOT put these in your .env.local file.

# --- Authentication ---
# Required for GitHub login.
AUTH_GITHUB_ID="your-github-oauth-client-id"
AUTH_GITHUB_SECRET="your-github-oauth-client-secret"

# --- AI Provider API Keys (for built-in platform usage) ---
# Add keys for any services you want to provide through the platform.
OPENAI_API_KEY="sk-..."
ANTHROPIC_API_KEY="sk-ant-..."
GOOGLE_API_KEY="AIza..."
OPENROUTER_API_KEY="sk-or-..."
GROQ_API_KEY="gsk_..."
DEEPSEEK_API_KEY="sk-..."
MISTRAL_API_KEY="..."
COHERE_API_KEY="..."
GROK_API_KEY="..."

# --- Tool API Keys ---
# Required for specific tools to function.
TAVILY_API_KEY="tvly-..."
OPENWEATHER_API_KEY="..."