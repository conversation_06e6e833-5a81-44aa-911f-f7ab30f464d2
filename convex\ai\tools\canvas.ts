import { tool } from "ai";
import { z } from "zod";

export const canvasTool = tool({
  description: `Create a rich canvas for three distinct purposes:

1. markdown — Long-form writing (essays, docs, knowledge bases). **Markdown is strictly for text; DO NOT embed Mermaid diagrams here.**
2. code — Interactive web projects written in HTML/CSS/JS (single-file demos, widgets, prototypes).
3. chart — Programmatic chart creation (e.g. Chart.js, ECharts, D3). Provide executable front-end code; the agent will render the chart inside a self-contained HTML document.

When to use this tool:
• The user asks to draft or heavily edit long documents.
• The user wants a live web preview, UI mock-up, or small app.
• The user needs data visualisation / charts.

Avoid using canvas for:
• Quick Q&A style responses.
• Mermaid diagrams (use a dedicated diagram tool instead).
• Tiny illustrative code snippets that don't need a live preview.`,

  parameters: z.object({
    type: z
      .enum(["markdown", "code", "chart"])
      .describe("Canvas mode: 'markdown', 'code', or 'chart'"),
    title: z
      .string()
      .describe("Short, descriptive title for the canvas content"),
    content: z
      .string()
      .describe(
        "Main body: markdown text, or full HTML/CSS/JS when type ≠ 'markdown'. Use empty string when type = 'chart' and 'chartSpec' is provided."
      ),
    language: z
      .string()
      .describe("Programming language for 'code' canvas (html, css, js, etc.). Use empty string if not applicable."),
    chartSpec: z
      .string()
      .describe(
        "(chart only) JSON or JS snippet describing the chart configuration. Use empty string if not a chart."
      ),
    library: z
      .enum(["chartjs", "echarts", "d3"])
      .describe(
        "(chart only) Preferred JS charting library. Use 'chartjs' as default."
      ),
  }),

  execute: async ({ type, title, content, language, chartSpec, library }) => {
    let finalContent = content;

    if (type === "chart") {
      if (!chartSpec) {
        throw new Error("chartSpec is required when type is 'chart'");
      }

      // If content not provided, create a self-contained HTML using Chart.js (default) or specified library
      if (!finalContent) {
        const lib = library || "chartjs";
        if (lib === "chartjs") {
          finalContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>${title}</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <canvas id="myChart"></canvas>
  <script>
    const ctx = document.getElementById('myChart').getContext('2d');
    const spec = ${chartSpec};
    new Chart(ctx, spec);
  </script>
</body>
</html>`;
        } else {
          // For echarts or d3, leave placeholder for user-provided code
          finalContent = `<pre style="color:red">Please provide 'content' with HTML/JS for library ${lib}.</pre>`;
        }
      }
    }

    return {
      type,
      title,
      content: finalContent || "",
      language,
      chartSpec,
      library,
      updatedAt: Date.now(),
    };
  },
});
