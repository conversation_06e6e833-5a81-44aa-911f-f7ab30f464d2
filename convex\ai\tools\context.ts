"use node";

import { tool } from "ai";
import { z } from "zod";
import { internal } from "../../_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

// Vector search tool – fetches relevant snippets from the current conversation
export function createContextTool(ctx: any) {
  return tool({
    description:
      "Retrieve the most relevant snippets from ALL of the user's conversations via semantic search. Optionally limit to a single conversation by passing conversation_id. Use this to ground answers in prior messages when extra context is helpful.",
    parameters: z.object({
      user_id: z
        .string()
        .describe(
          "The ID of the current user. Use empty string to use authenticated user."
        ),
      conversation_id: z
        .string()
        .describe("Restrict to a single conversation. Use empty string for all conversations."),
      branch_id: z
        .string()
        .describe("Specific branch ID if using branches. Use empty string for main branch."),
      query: z
        .string()
        .describe("What you are searching for in the conversation."),
      top_k: z
        .number()
        .describe("Max number of snippets to return (use 5 as default)."),
    }),
    execute: async ({
      user_id,
      conversation_id,
      branch_id,
      query,
      top_k,
    }) => {
      // Handle defaults
      if (!top_k) {
        top_k = 5;
      }
      let uid = user_id;
      if (!uid) {
        // try to resolve from auth context
        uid = (await getAuthUserId(ctx)) as unknown as string;
      }

      if (!uid) {
        throw new Error(
          "Missing user_id and no authenticated user found; cannot search context."
        );
      }

      const results = await ctx.runAction(
        (internal as any).vectorize.searchContext,
        {
          userId: uid as any,
          conversationId: conversation_id ?? undefined,
          branchId: branch_id,
          query,
          topK: top_k,
        }
      );

      if (!results?.length) {
        return "No relevant context found.";
      }

      return results
        .map(
          (r: any, i: number) =>
            `#${i + 1} (${r.score.toFixed(3)}) [${r.role}] ${r.content}`
        )
        .join("\n\n");
    },
  });
}
