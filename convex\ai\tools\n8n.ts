"use node";

import { z } from "zod";
import { JSONSchemaToZod } from "@dmitryrechkin/json-schema-to-zod";
import type { Doc } from "../../_generated/dataModel";
import fetch from "node-fetch";

// Function to execute an n8n workflow via API
export async function executeN8nWorkflow(
  workflow: Doc<"n8nWorkflows">,
  params: Record<string, any>
) {
  try {
    // Build the correct endpoint depending on the trigger type
    // 1. Webhook → use the provided URL directly (default POST)
    // 2. API     → call n8n public REST API "execute" endpoint
    const endpoint =
      workflow.triggerType === "webhook"
        ? (workflow.webhookUrl as string)
        : (() => {
            // Ensure apiUrl does not end with a trailing slash
            const base = (workflow.apiUrl as string).replace(/\/+$/, "");
            // Use the public REST API to start a new execution
            return `${base}/api/v1/workflows/${workflow.workflowId}/executions`;
          })();

    // Set up headers (JSON body + optional API key)
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Add authentication if provided
    if (workflow.apiKey) {
      headers["X-N8N-API-KEY"] = workflow.apiKey;
    }

    // Make the request to n8n
    const response = await fetch(endpoint, {
      method: "POST",
      headers,
      // Always send params inside a JSON payload. n8n will expose them under
      // $json when received via webhook, and under the "data" field for the
      // execute endpoint.
      body: JSON.stringify({ ...params }),
      // 60 s timeout to avoid hanging forever
      timeout: 60_000,
    } as any);

    // Handle possible errors
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `n8n workflow execution failed (${response.status}): ${errorText}`
      );
    }

    // Attempt to parse JSON; if it fails, fall back to plain text
    let result: any;
    const raw = await response.text();
    try {
      result = JSON.parse(raw);
    } catch {
      result = raw;
    }

    return result;
  } catch (error) {
    console.error(`Error executing n8n workflow ${workflow.name}:`, error);
    throw new Error(
      `Failed to execute workflow ${workflow.name}: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
  }
}

// Function to validate an n8n connection
export async function validateN8nConnection(
  apiUrl: string,
  apiKey?: string
): Promise<boolean> {
  try {
    const headers: Record<string, string> = {};
    if (apiKey) {
      headers["X-N8N-API-KEY"] = apiKey;
    }

    // Try to get workflows to validate connection
    const base = apiUrl.replace(/\/+$/, "");
    const response = await fetch(`${base}/api/v1/workflows`, {
      headers,
      timeout: 10000, // 10 second timeout
    } as any);

    return response.ok;
  } catch (error) {
    console.error("Failed to validate n8n connection:", error);
    return false;
  }
}

// Function to fetch available workflows from n8n
export async function fetchN8nWorkflows(
  apiUrl: string,
  apiKey?: string
): Promise<Array<{ id: string; name: string; active: boolean }>> {
  try {
    const headers: Record<string, string> = {};
    if (apiKey) {
      headers["X-N8N-API-KEY"] = apiKey;
    }

    const base = apiUrl.replace(/\/+$/, "");
    const response = await fetch(`${base}/api/v1/workflows`, {
      headers,
      timeout: 10000,
    } as any);

    if (!response.ok) {
      throw new Error(`Failed to fetch workflows: ${response.statusText}`);
    }

    const data = (await response.json()) as {
      data: Array<{ id: string; name: string; active: boolean }>;
    };
    return data.data.map((wf) => ({
      id: wf.id,
      name: wf.name,
      active: wf.active,
    }));
  } catch (error) {
    console.error("Failed to fetch n8n workflows:", error);
    return [];
  }
}

// Function to create AI tools from n8n workflows
export function createN8nTools(workflows: Doc<"n8nWorkflows">[]) {
  const tools: Record<string, any> = {};

  // Create a tool for each active workflow
  workflows
    .filter((workflow) => workflow.isEnabled)
    .forEach((workflow) => {
      const rawName =
        workflow.name && workflow.name.trim()
          ? workflow.name.trim()
          : `workflow_${String(workflow._id).slice(-6)}`;

      // Create a sanitized ID for the tool (ensure at least one character)
      let sanitized = rawName.toLowerCase().replace(/[^a-z0-9]/g, "_");
      if (sanitized === "")
        sanitized = `workflow_${String(workflow._id).slice(-6)}`;
      const toolId = `n8n_${sanitized}`;

      // Define parameters based on workflow parameters schema
      let parameters: z.ZodObject<any> = z.object({});

      try {
        if (workflow.parametersSchema) {
          const schema = JSON.parse(workflow.parametersSchema);
          parameters = JSONSchemaToZod.convert(schema) as z.ZodObject<any>;
        }
      } catch (error) {
        console.error(
          `Failed to parse parameters schema for workflow ${workflow.name}:`,
          error
        );
      }

      // Create the tool
      tools[toolId] = {
        description: workflow.description || `Execute n8n workflow: ${rawName}`,
        parameters,
        execute: async (params: Record<string, any>) => {
          return await executeN8nWorkflow(workflow, params);
        },
      };
    });

  return tools;
}

// Function to get tool info for UI without creating full tools
export function getN8nToolInfo(workflows: Doc<"n8nWorkflows">[]) {
  return workflows
    .filter((workflow) => workflow.isEnabled)
    .map((workflow) => {
      const rawName =
        workflow.name && workflow.name.trim()
          ? workflow.name.trim()
          : `workflow_${String(workflow._id).slice(-6)}`;

      let sanitized = rawName.toLowerCase().replace(/[^a-z0-9]/g, "_");
      if (sanitized === "")
        sanitized = `workflow_${String(workflow._id).slice(-6)}`;

      return {
        id: `n8n_${sanitized}`,
        name: `n8n: ${rawName}`,
        description: workflow.description || `Execute n8n workflow: ${rawName}`,
        category: "n8n",
        workflowId: workflow._id,
        triggerType: workflow.triggerType,
      };
    });
}
