"use node";

import { tool } from "ai";
import { z } from "zod";

export function createThinkingTool() {
  return tool({
    description:
      `You are an advanced reasoning engine. Your task is to externalize every step of your thought process in a transparent, human-like Chain-of-Thought format. Follow this structure:

1. **Context**: Briefly restate the problem to ensure clarity.
2. **Decomposition**: Break the problem into concise sub-questions.
3. **Analysis**: For each sub-question, reason step-by-step and note key observations.
4. **Alternatives**: List possible approaches, with pros and cons for each.
5. **Plan**: Outline a prioritized sequence of concrete reasoning steps, including rationale, complexity estimate, and verification checkpoints.
6. **Synthesis**: Summarize how the plan resolves the problem.

After the Chain-of-Thought, separate with a horizontal rule (---) and then present a clear, concise final answer.`,
    parameters: z.object({
      problem: z
        .string()
        .describe(
          "A clear and concise statement of the complex problem to be solved."
        ),
      subQuestions: z
        .array(z.string())
        .describe(
          "Break down of the main problem into smaller, manageable questions."
        ),
      approaches: z
        .array(
          z.object({
            approach: z
              .string()
              .describe("A possible approach to solve the problem."),
            pros: z.array(z.string()).describe("Advantages of this approach."),
            cons: z
              .array(z.string())
              .describe("Disadvantages of this approach."),
          })
        )
        .describe(
          "Analysis of different possible approaches to solve the problem."
        ),
      plan: z
        .array(
          z.object({
            step: z
              .string()
              .describe(
                "A single, clear action or analysis step in your reasoning process."
              ),
            reasoning: z
              .string()
              .describe(
                "The justification and expected outcome for this specific step."
              ),
            priority: z
              .number()
              .describe("Priority of this step (1 = highest priority)"),
            complexity: z
              .string()
              .describe(
                "Estimated complexity or time required for this step (e.g., 'Low', 'Medium', 'High', '10 minutes')"
              ),
            checkpoints: z
              .array(z.string())
              .describe(
                "Suggested checkpoints or tests to verify progress on this step."
              ),
          })
        )
        .describe("An array of steps outlining the plan to solve the problem."),
      conclusion: z
        .string()
        .describe(
          "A summary of the expected final answer or conclusion based on the execution of the plan."
        ),
    }),
    execute: async ({ problem, subQuestions, approaches, plan, conclusion }) => {
      // 1. Context
      let output = `**Context:** ${problem}\n\n`;

      // 2. Decomposition
      output += `**Decomposition:**\n`;
      subQuestions.forEach((q, i) => {
        output += `${i + 1}. ${q}\n`;
      });
      output += `\n`;

      // 3. Analysis
      output += `**Analysis:**\n`;
      subQuestions.forEach((q, i) => {
        output += `- Observation on Q${i + 1}: ${q}\n`;
        output += `- Reasoning: ... (detail your step-by-step thoughts)\n`;
      });
      output += `\n`;

      // 4. Alternatives
      output += `**Alternatives:**\n`;
      approaches.forEach((a, i) => {
        output += `${i + 1}. Approach: ${a.approach}\n`;
        output += `   - Pros: ${a.pros.join(", ")}\n`;
        output += `   - Cons: ${a.cons.join(", ")}\n`;
      });
      output += `\n`;

      // 5. Plan
      output += `**Plan:**\n`;
      plan
        .sort((a, b) => a.priority - b.priority)
        .forEach((stepObj, i) => {
          output += `${i + 1}. Step: ${stepObj.step}\n`;
          output += `   - Why: ${stepObj.reasoning}\n`;
          output += `   - Complexity: ${stepObj.complexity}\n`;
          output += `   - Checkpoints: ${stepObj.checkpoints.join(", ")}\n`;
        });
      output += `\n`;

      // 6. Synthesis and Conclusion
      output += `**Synthesis:** The outlined plan addresses each sub-question and balances trade-offs.\n\n`;
      output += `---\n\n`;
      output += `**Answer:** ${conclusion}\n`;

      return output;
    },
  });
}
