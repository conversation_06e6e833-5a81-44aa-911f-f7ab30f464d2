"use node";

import { tool } from "ai";
import { z } from "zod";
import { browseUrl } from "../utils/browse";
import { api } from "../../_generated/api";

export function createUrlFetchTool(ctx: any) {
  return tool({
    description: `Retrieve **detailed content** from a single web page (HTML, text, metadata) using Firecrawl.

When to use:
• You already know the exact URL (for example, the user pasted it or you discovered it via web_search) **and** you need more than the snippet.
• You want to summarise, extract key facts, or quote text from that page.

When *not* to use:
• For broad discovery or searching – use \`web_search\` first.
• When you can confidently answer without reading the page.

Parameters:
• \`url\` – the absolute URL to fetch.
• \`analysis_type\` – optional: "content" (default, full cleaned article), "summary" (short summary), "extract" (structured key facts), or "metadata" (title, description, etc.).

Be mindful of user-disabled tools: only call this if \`url_fetch\` is enabled.`,
    parameters: z.object({
      url: z
        .string()
        .describe("The specific URL to fetch and analyze content from"),
      analysis_type: z
        .enum(["content", "summary", "extract", "metadata"])
        .describe(
          "How to process the content: full content, summary, extract key information, or just metadata"
        ),
    }),
    execute: async ({ url, analysis_type }): Promise<string> => {
      if (!analysis_type) {
        analysis_type = "content" as any;
      }
      // Get Firecrawl API key from the context
      const apiKeyRecord = await ctx.runQuery(api.apiKeys.getByProvider, {
        provider: "firecrawl",
      });

      let apiKey = "";
      let usingUserKey = false;

      // PRIORITIZE USER'S API KEY FIRST
      if (apiKeyRecord?.apiKey && apiKeyRecord.apiKey.trim().length > 0) {
        apiKey = apiKeyRecord.apiKey.trim();
        usingUserKey = true;
      } else {
        // Use built-in Firecrawl key as fallback
        apiKey = process.env.FIRECRAWL_API_KEY || "";
      }

      if (!apiKey) {
        return `Error: Firecrawl API key not configured. Please add your Firecrawl API key in settings to use URL fetching, or set FIRECRAWL_API_KEY environment variable.`;
      }

      return await browseUrl(url, analysis_type, apiKey);
    },
  });
}
