"use node";

import { tool } from "ai";
import { z } from "zod";
import { performTavilySearch } from "../utils/search";
import { api } from "../../_generated/api";

export function createWebSearchTool(ctx: any, usingUser<PERSON>ey: boolean) {
  return tool({
    description:
      `Run a **quick web search** (advanced depth with images) for fresh information and an overview of a topic. Returns up to 20 rich results with thumbnails when available.\n\nWhen to use:\n• You need recent news, live data, or external references.\n• You want a list of sources to decide which ones to read in full (via \`url_fetch\`).\n\nAfter calling this, usually follow-up with \`url_fetch\` on 1-3 of the most relevant links if the snippets are insufficient.`,
    parameters: z.object({
      query: z
        .string()
        .describe("The search query for current/real-time information"),
    }),
    execute: async ({ query }): Promise<string> => {
      return await performTavilySearch(ctx, query, "advanced", !usingU<PERSON><PERSON>ey);
    },
  });
}

export function createDeepSearchTool(ctx: any, usingUser<PERSON>ey: boolean) {
  return tool({
    description:
      `Run an **in-depth multi-query search** (advanced depth with images), ideal for complex research. It issues several focused searches and merges up to 20 results per query.\n\nWhen to use:\n• You must gather perspectives from many sources, or the single web search didn't yield enough detail.\n\nTypical follow-up: use \`url_fetch\` on promising links to extract full content or facts.`,
    parameters: z.object({
      query: z
        .string()
        .describe("The main research topic requiring current information"),
      related_queries: z
        .array(z.string())
        .describe(
          "Additional specific research angles for comprehensive coverage"
        ),
    }),
    execute: async ({ query, related_queries = [] }): Promise<string> => {
      // Only increment search usage if using built-in keys
      if (!usingUserKey) {
        // Deep search uses additional search quota
        await ctx.runMutation(api.usage.incrementSearches);
        await ctx.runMutation(api.usage.incrementSearches);
      }

      const allQueries = [query, ...related_queries.slice(0, 3)]; // Limit to 4 total queries
      const searchPromises = allQueries.map((q) =>
        performTavilySearch(ctx, q, "advanced", !usingUserKey)
      );
      const results = await Promise.all(searchPromises);

      let combinedResults = `Deep search results for "${query}":\n\n`;
      results.forEach((result, index) => {
        combinedResults += `=== Results for "${allQueries[index]}" ===\n${result}\n\n`;
      });

      return combinedResults;
    },
  });
}
