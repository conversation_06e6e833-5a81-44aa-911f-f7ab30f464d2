"use node";

import { action } from "./_generated/server";
import { v } from "convex/values";
import { Octokit } from "octokit";
import { internal } from "./_generated/api";

/**
 * Node runtime action to fetch and cache a GitHub repository branch.
 */
export const fetchRepo = action({
  args: {
    repoUrl: v.string(),
    branch: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Parse owner/repo
    const match = args.repoUrl.match(
      /github\.com\/([^/]+)\/([^/]+?)(?:\.git|$|\/)/
    );
    if (!match) throw new Error("Invalid GitHub repo URL");
    const owner = match[1];
    const repo = match[2];
    const repoFullName = `${owner}/${repo}`;

    // Check existing cache
    const meta: any = await ctx.runQuery(internal.repositories.getBranchMeta, {
      repoFullName,
      branch: args.branch,
    });

    // Try to use user's token
    let token: string | undefined;
    try {
      const account: any = await ctx.runQuery(internal.github.getAccount, {});
      if (account) token = account.accessToken;
    } catch {}

    const octokit = new Octokit({ auth: token });

    const branchResp = await octokit.rest.repos.getBranch({
      owner,
      repo,
      branch: args.branch,
    });
    const commitSha: string = (branchResp.data.commit as any).sha;

    if (meta && meta.commitSha === commitSha) return null; // Up-to-date

    // Build ignore pattern list
    const ignorePatterns: string[] = ["node_modules/", ".git/"];
    try {
      const gitResp = await octokit.rest.repos.getContent({
        owner,
        repo,
        path: ".gitignore",
        ref: args.branch,
      });
      if (!Array.isArray(gitResp.data) && "content" in gitResp.data) {
        const decoded = Buffer.from(
          (gitResp.data as any).content,
          "base64"
        ).toString("utf8");
        decoded
          .split(/\r?\n/)
          .map((l) => l.trim())
          .filter((l) => l && !l.startsWith("#"))
          .forEach((p) => ignorePatterns.push(p.endsWith("/") ? p : `${p}`));
      }
    } catch {}

    const treeResp = await octokit.rest.git.getTree({
      owner,
      repo,
      tree_sha: commitSha,
      recursive: "true",
    });
    const blobs = treeResp.data.tree.filter((t: any) => t.type === "blob");

    await ctx.runMutation(internal.repositories.upsertBranchMeta, {
      repoFullName,
      branch: args.branch,
      commitSha,
    });

    const isBinary = (buf: Buffer) => buf.some((b, i) => i < 24 && b === 0);

    for (const blob of blobs.slice(0, 3000)) {
      const path = blob.path as string;
      if (ignorePatterns.some((pat) => path.startsWith(pat))) continue;
      const sha = blob.sha as string;
      const size = blob.size ?? 0;
      let content: string | undefined = undefined;
      if (size <= 100 * 1024) {
        const fileResp = await octokit.rest.repos.getContent({
          owner,
          repo,
          path,
          ref: args.branch,
        });
        if (!Array.isArray(fileResp.data) && "content" in fileResp.data) {
          const buf = Buffer.from((fileResp.data as any).content, "base64");
          if (!isBinary(buf)) content = buf.toString("utf8");
        }
      }
      await ctx.runMutation(internal.repositories.upsertFile, {
        repoFullName,
        branch: args.branch,
        path,
        sha,
        size,
        content,
      });
    }

    return null;
  },
});
