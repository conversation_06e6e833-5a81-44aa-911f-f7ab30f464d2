"use node";

import { internalAction } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api";

// Name of the Cloudflare Vectorize index to store conversation embeddings.
// You can override this per-deployment by setting the CF_VECTORIZE_INDEX env var.
const VECTORIZE_INDEX_NAME = process.env.CF_VECTORIZE_INDEX || "conversations";

// Default Workers AI embedding model ID. Feel free to switch to another.
// https://developers.cloudflare.com/workers-ai/models/#text-embeddings
const EMBEDDING_MODEL = "@cf/baai/bge-m3" as const;

export const indexMessage = internalAction({
  // We accept the message text so the action can run without DB access.
  args: {
    messageId: v.id("messages"),
    conversationId: v.id("conversations"),
    userId: v.id("users"),
    branchId: v.string(),
    text: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // ---------------------------------------------------------------------
    // 1. Resolve Cloudflare credentials (user override → env default)
    // ---------------------------------------------------------------------
    const accountId = process.env.CLOUDFLARE_ACCOUNT_ID;
    if (!accountId) {
      console.warn(
        "CLOUDFLARE_ACCOUNT_ID env var is missing – skipping Vectorize indexing."
      );
      return null;
    }

    let apiToken = process.env.CLOUDFLARE_API_TOKEN || "";

    try {
      // Attempt to fetch a user-specific key if available & active
      const userKeyRecord = await ctx.runQuery(api.apiKeys.getByProvider, {
        provider: "cloudflare" as any, // cast because getByProvider has literal union type
      });
      if (userKeyRecord?.apiKey && userKeyRecord.isActive) {
        apiToken = userKeyRecord.apiKey.trim();
      }
    } catch (err) {
      // If the apiKeys table doesn't have cloudflare yet, carry on with env vars.
    }

    if (!apiToken) {
      console.warn(
        "No Cloudflare API token found – skipping Vectorize indexing."
      );
      return null;
    }

    // ---------------------------------------------------------------------
    // 2. Generate an embedding with Workers AI
    // ---------------------------------------------------------------------
    const embedUrl = `https://api.cloudflare.com/client/v4/accounts/${accountId}/ai/run/${EMBEDDING_MODEL}`;

    const embedResp = await fetch(embedUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${apiToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        // Workers AI expects either a single string or an array of strings under the **text** key
        text: [args.text],
      }),
    });

    if (!embedResp.ok) {
      console.error(
        "Workers AI embedding request failed:",
        embedResp.status,
        await embedResp.text().catch(() => "<no text>")
      );
      return null;
    }

    const embedJson: any = await embedResp.json();

    // Workers AI embedding responses can be one of:
    // { data: [[...]], shape: [...], pooling: "mean" }
    // { result: [...] }
    // { result: { data: [[...]] } }
    let vector: number[] | undefined;
    if (Array.isArray(embedJson)) {
      vector = embedJson as number[]; // unlikely but safeguard
    } else if (Array.isArray(embedJson.data)) {
      vector = embedJson.data[0];
    } else if (Array.isArray(embedJson.result)) {
      vector = embedJson.result[0];
    } else if (Array.isArray(embedJson.result?.data)) {
      vector = embedJson.result.data[0];
    }

    if (!vector || !Array.isArray(vector)) {
      console.error("Unexpected embedding response structure", embedJson);
      return null;
    }

    // ---------------------------------------------------------------------
    // 3. Ensure the Vectorize index exists (create if absent)
    // ---------------------------------------------------------------------
    const baseUrl = `https://api.cloudflare.com/client/v4/accounts/${accountId}/vectorize/v2/indexes`;
    const indexUrl = `${baseUrl}/${VECTORIZE_INDEX_NAME}`;

    let indexExists = false;
    try {
      const getResp = await fetch(indexUrl, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${apiToken}`,
        },
      });
      indexExists = getResp.ok;
    } catch (_) {
      indexExists = false;
    }

    if (!indexExists) {
      try {
        const createResp = await fetch(baseUrl, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${apiToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: VECTORIZE_INDEX_NAME,
            description: "Convex conversations index",
            config: {
              dimensions: vector.length,
              metric: "cosine",
            },
          }),
        });

        if (!createResp.ok) {
          console.error(
            "Failed to create Vectorize index:",
            createResp.status,
            await createResp.text().catch(() => "<no text>")
          );
        } else {
          console.log(
            `Created Vectorize index '${VECTORIZE_INDEX_NAME}' (dims ${vector.length}).`
          );
        }
      } catch (err) {
        console.error("Error creating Vectorize index", err);
      }
    }

    // ---------------------------------------------------------------------
    // 4. Upsert the vector into Vectorize
    // ---------------------------------------------------------------------
    const upsertUrl = `${indexUrl}/upsert`;

    // Vectorize v2 expects newline-delimited JSON (NDJSON) with content-type x-ndjson
    const ndjsonLine = JSON.stringify({
      id: args.messageId,
      values: vector,
      metadata: {
        conversationId: args.conversationId,
        branchId: args.branchId,
        userId: args.userId,
      },
    });

    const upsertResp = await fetch(upsertUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${apiToken}`,
        "Content-Type": "application/x-ndjson",
      },
      body: ndjsonLine + "\n",
    });

    if (!upsertResp.ok) {
      console.error(
        "Vectorize upsert failed:",
        upsertResp.status,
        await upsertResp.text().catch(() => "<no text>")
      );
      return null;
    }

    // Optional: log success for debugging
    console.log(
      `Indexed message ${args.messageId} (conversation ${args.conversationId}) into Vectorize.`
    );
    return null;
  },
});

// ---------------------------------------------------------------------------
// Search vectors for relevant context within a conversation
// ---------------------------------------------------------------------------

export const searchContext: any = internalAction({
  args: {
    userId: v.id("users"),
    conversationId: v.optional(v.id("conversations")),
    branchId: v.optional(v.string()),
    query: v.string(),
    topK: v.optional(v.number()),
  },
  returns: v.array(
    v.object({
      _id: v.id("messages"),
      role: v.string(),
      content: v.string(),
      score: v.number(),
    })
  ),
  handler: async (ctx, args) => {
    const accountId = process.env.CLOUDFLARE_ACCOUNT_ID;
    const apiToken = process.env.CLOUDFLARE_API_TOKEN;
    if (!accountId || !apiToken) {
      console.warn("Cloudflare creds missing – searchContext skipped");
      return [];
    }

    // 1. Embed the query text
    const embedUrl = `https://api.cloudflare.com/client/v4/accounts/${accountId}/ai/run/${EMBEDDING_MODEL}`;
    const embedResp = await fetch(embedUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${apiToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ text: [args.query] }),
    });
    if (!embedResp.ok) {
      console.error("Query embedding failed", await embedResp.text());
      return [];
    }
    const embedJson: any = await embedResp.json();
    const vector: number[] | undefined = Array.isArray(embedJson.data)
      ? embedJson.data[0]
      : embedJson.result?.data?.[0] || embedJson.result?.[0];
    if (!vector) {
      return [];
    }

    // 2. Query Vectorize index
    const baseUrl = `https://api.cloudflare.com/client/v4/accounts/${accountId}/vectorize/v2/indexes`;
    const queryUrl = `${baseUrl}/${VECTORIZE_INDEX_NAME}/query`;
    const queryBody = {
      vector,
      topK: args.topK ?? 5,
      returnMetadata: "all" as const,
    };
    const queryResp = await fetch(queryUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${apiToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(queryBody),
    });
    if (!queryResp.ok) {
      console.error("Vectorize query failed", await queryResp.text());
      return [];
    }
    const qJson: any = await queryResp.json();
    const matches = qJson.result?.matches ?? qJson.matches ?? [];

    // Filter: only this user's data, and optionally a single conversation
    const filtered = matches.filter((m: any) => {
      if (m.metadata?.userId !== args.userId) return false;
      if (
        args.conversationId &&
        m.metadata?.conversationId !== args.conversationId
      )
        return false;
      return true;
    });

    const ids = filtered.slice(0, args.topK ?? 5).map((m: any) => m.id);
    if (ids.length === 0) {
      return [];
    }

    const messagesApi: any = api.messages as any;
    const snippets = await ctx.runQuery(messagesApi.getSnippets, { ids });
    // attach score ensuring type safety
    return snippets.map(
      (snippet: { _id: string; role: string; content: string }) => ({
        ...snippet,
        score: filtered.find((m: any) => m.id === snippet._id)?.score ?? 0,
      })
    );
  },
});
