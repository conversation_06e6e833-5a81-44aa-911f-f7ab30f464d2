// App.tsx
import { Authenticated, Unauthenticated } from "convex/react";
import { Toaster } from "sonner";
import ChatInterface from "./components/ChatInterface";
import { SharedConversation } from "./components/SharedConversation";
import { Homepage } from "./components/Homepage";
import { SettingsPage } from "./components/SettingsPage";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { LoginPage } from "./components/LoginPage";
import { Redirect } from "./components/Redirect";
import { LibraryPage } from "./components/LibraryPage";
import { ReposePage } from "./components/ReposePage";
import { PrivacyPolicyPage } from "./components/PrivacyPolicyPage";
import { TermsPage } from "./components/TermsPage";

function AppContent() {
  const { theme } = useTheme();
  const [currentRoute, setCurrentRoute] = useState(window.location.pathname);

  useEffect(() => {
    const handlePopState = () => {
      setCurrentRoute(window.location.pathname);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  // Shared conversation route (public)
  const sharedMatch = currentRoute.match(/^\/shared\/(.+)$/);
  const shareId = sharedMatch?.[1];
  if (shareId) {
    return (
      <>
        <SharedConversation shareId={shareId} />
        <Toaster theme={theme as "light" | "dark" | "system"} richColors />
      </>
    );
  }

  // Homepage route (public)
  if (currentRoute === '/homepage') {
    return (
      <>
        <Homepage />
        <Toaster theme={theme as "light" | "dark" | "system"} richColors />
      </>
    );
  }

  // Login page route
  if (currentRoute === '/login') {
    return (
      <>
        <Authenticated>
          <Redirect to="/" />
        </Authenticated>
        <Unauthenticated>
          <LoginPage />
        </Unauthenticated>
        <Toaster theme={theme as "light" | "dark" | "system"} richColors />
      </>
    );
  }

  // Settings page route (authenticated)
  if (currentRoute === '/settings') {
    return (
      <>
        <Authenticated>
          <div className="min-h-screen flex flex-col bg-gradient-to-br from-background via-background/98 to-muted/20 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-tr from-primary/2 via-transparent to-accent/2 pointer-events-none"></div>
            <div className="relative z-10 min-h-screen flex flex-col">
              <SettingsPage onBack={() => {
                window.history.pushState({}, '', '/');
                window.dispatchEvent(new PopStateEvent('popstate'));
              }} />
            </div>
          </div>
        </Authenticated>
        <Unauthenticated>
          <Redirect to="/homepage" />
        </Unauthenticated>
        <Toaster theme={theme as "light" | "dark" | "system"} richColors />
      </>
    );
  }

  // Library page route (authenticated)
  if (currentRoute === '/library') {
    return (
      <>
        <Authenticated>
          <div className="min-h-screen flex flex-col bg-gradient-to-br from-background via-background/98 to-muted/20 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-tr from-primary/2 via-transparent to-accent/2 pointer-events-none"></div>
            <div className="relative z-10 min-h-screen flex flex-col">
              <ChatInterface mainContent={<LibraryPage />} />
            </div>
          </div>
        </Authenticated>
        <Unauthenticated>
          <Redirect to="/homepage" />
        </Unauthenticated>
        <Toaster theme={theme as "light" | "dark" | "system"} richColors />
      </>
    );
  }

  // Repose page route (authenticated)
  if (currentRoute === '/repose') {
    return (
      <>
        <Authenticated>
          <div className="min-h-screen flex flex-col bg-gradient-to-br from-background via-background/98 to-muted/20 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-tr from-primary/2 via-transparent to-accent/2 pointer-events-none"></div>
            <div className="relative z-10 min-h-screen flex flex-col">
              <ChatInterface mainContent={<ReposePage />} />
            </div>
          </div>
        </Authenticated>
        <Unauthenticated>
          <Redirect to="/homepage" />
        </Unauthenticated>
        <Toaster theme={theme as "light" | "dark" | "system"} richColors />
      </>
    );
  }

  // Privacy Policy route
  if (currentRoute === '/privacy') {
    return (
      <>
        <PrivacyPolicyPage />
        <Toaster theme={theme as "light" | "dark" | "system"} richColors />
      </>
    );
  }

  // Terms route
  if (currentRoute === '/terms') {
    return (
      <>
        <TermsPage />
        <Toaster theme={theme as "light" | "dark" | "system"} richColors />
      </>
    );
  }

  // Root route (/)
  return (
    <>
      <Authenticated>
        <div className="min-h-screen flex flex-col bg-gradient-to-br from-background via-background/98 to-muted/20 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-tr from-primary/2 via-transparent to-accent/2 pointer-events-none"></div>
          <div className="relative z-10 min-h-screen flex flex-col">
            <ChatInterface />
          </div>
        </div>
      </Authenticated>
      <Unauthenticated>
        <Redirect to="/homepage" />
      </Unauthenticated>
      <Toaster theme={theme as "light" | "dark" | "system"} richColors />
    </>
  );
}

export default function App() {
  return <AppContent />;
}