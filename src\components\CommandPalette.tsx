import { usePaginatedQuery, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import {
  CommandDialog,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandSeparator,
} from "@/components/ui/command";
import { Hash, MessageSquare, Pin } from "lucide-react";
import React, { useState } from "react";

interface CommandPaletteProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectConversation: (id: Id<"conversations">) => void;
}

export function CommandPalette({
  open,
  onOpenChange,
  onSelectConversation,
}: CommandPaletteProps) {
  const [query, setQuery] = useState("");
  const [debouncedQuery, setDebouncedQuery] = useState("");

  // Debounce input
  React.useEffect(() => {
    const id = setTimeout(() => setDebouncedQuery(query.trim()), 300);
    return () => clearTimeout(id);
  }, [query]);

  // Default: show 10 most recent
  const { results: conversations } = usePaginatedQuery(
    api.conversations.listWithMessageCounts,
    open && debouncedQuery === "" ? { paginationOpts: { numItems: 10, cursor: null } } : "skip",
    { initialNumItems: 10 }
  );

  // Search: show up to 50 results
  const searchResults = useQuery(
    api.conversations.searchConversations,
    open && debouncedQuery !== "" ? { query: debouncedQuery, limit: 10 } : "skip"
  );

  const loading = debouncedQuery !== "" && searchResults === undefined;
  const page = debouncedQuery !== "" ? (searchResults ?? []) : (conversations ?? []);

  const pinned = page.filter((c: any) => c.isPinned);
  const others = page.filter((c: any) => !c.isPinned);

  const handleOpenChange = (o: boolean) => {
    onOpenChange(o);
    if (!o) {
      setQuery("");
      setDebouncedQuery("");
    }
  };

  const handleInputKeyDown = () => {};

  const renderItem = (conv: any) => (
    <CommandItem
      key={conv._id}
      value={conv.title}
      onSelect={() => {
        onSelectConversation(conv._id);
        handleOpenChange(false);
      }}
      className="flex-col items-start gap-1 py-2 px-3 rounded-xl"
    >
      <div className="flex w-full items-center gap-3">
        {conv.isPinned ? (
          <Pin className="w-4 h-4 text-primary" />
        ) : (
          <MessageSquare className="w-4 h-4 text-primary" />
        )}
        <span className="truncate font-medium">{conv.title}</span>
        {conv.messageCount > 0 && (
          <span className="ml-auto flex items-center gap-1.5 text-xs text-muted-foreground font-mono">
            <Hash size={12} />
            {conv.messageCount}
          </span>
        )}
      </div>
      <div className="w-full flex text-xs text-muted-foreground gap-2">
        <span>{new Date(conv.lastMessageAt).toLocaleDateString()}</span>
        <span className="ml-auto opacity-60">{String(conv._id).slice(-4)}</span>
        </div>
    </CommandItem>
  );

  return (
    <CommandDialog open={open} onOpenChange={handleOpenChange}>
      <CommandInput
        placeholder="Search conversations..."
        value={query}
        onValueChange={setQuery}
        onKeyDown={handleInputKeyDown}
      />
      <CommandList>
        {debouncedQuery === "" && page.length === 0 && (
          <CommandEmpty>No conversations found.</CommandEmpty>
        )}
        {debouncedQuery === "" && page.length > 0 && (
          <CommandGroup heading="Recent">
            {page.map(renderItem)}
          </CommandGroup>
        )}
        {debouncedQuery !== "" && loading && (
          <CommandEmpty>Searching…</CommandEmpty>
        )}
        {debouncedQuery !== "" && !loading && page.length === 0 && (
          <CommandEmpty>No conversations found.</CommandEmpty>
        )}
        {debouncedQuery !== "" && !loading && (
          <>
            {pinned.length > 0 && (
              <CommandGroup heading="Pinned">
                {pinned.map(renderItem)}
              </CommandGroup>
            )}
            {pinned.length > 0 && others.length > 0 && <CommandSeparator />}
            {others.length > 0 && (
              <CommandGroup heading="Conversations">
                {others.map(renderItem)}
              </CommandGroup>
            )}
          </>
        )}
      </CommandList>
    </CommandDialog>
  );
} 