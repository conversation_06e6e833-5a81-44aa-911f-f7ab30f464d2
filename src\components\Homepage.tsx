import { <PERSON><PERSON> } from "./ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Badge } from "./ui/badge";
import { 
  Brain, 
  Sparkles, 
  Zap, 
  Shield, 
  Users, 
  MessageSquare, 
  ArrowRight,
  Star,
  ChevronRight,
  Globe,
  Code,
  Search,
  Image,
  Check,
  Clock,
  Github,
  Twitter
} from "lucide-react";
import { ParticlesBackground } from "./ParticlesBackground";

const features = [
  {
    icon: Brain,
    title: "Multi-Model AI Access",
    description: "Choose from o3, o1, GPT-4o, Claude 4.0, Gemini 2.5 Pro, DeepSeek R1, and 75+ other cutting-edge models. Switch seamlessly between providers to find the perfect AI for your task."
  },
  {
    icon: Zap,
    title: "Real-Time Streaming",
    description: "Experience lightning-fast responses with streaming technology. Watch your answers appear in real-time for an interactive conversation experience."
  },
  {
    icon: Shield,
    title: "Privacy & Security",
    description: "Your data stays secure with enterprise-grade encryption. We never store your conversations permanently, and you maintain full control over your information."
  },
  {
    icon: Users,
    title: "Conversation Sharing",
    description: "Share your AI conversations with teammates or friends via secure links. Collaborate on complex problems and build on each other's insights."
  },
  {
    icon: Code,
    title: "Advanced Code Assistant",
    description: "Get expert help with programming across 50+ languages. From debugging to code reviews, architecture advice to performance optimization."
  },
  {
    icon: Search,
    title: "Web Search Integration",
    description: "Access real-time information from the web. Get current news, research data, and up-to-date information integrated directly into your conversations."
  },
  {
    icon: Image,
    title: "Image Generation & Analysis",
    description: "Generate stunning images with AI or analyze existing ones. Perfect for creative projects, presentations, and visual problem-solving."
  },
  {
    icon: MessageSquare,
    title: "Smart Memory System",
    description: "Your AI remembers context across conversations. Build long-term relationships with personalized responses that get better over time."
  },
  {
    icon: Globe,
    title: "Multi-Language Support",
    description: "Communicate in 50+ languages with native-level fluency. Perfect for international teams, language learning, and global collaboration."
  }
];

const stats = [
  { label: "AI Models", value: "75+" },
  { label: "Languages Supported", value: "50+" },
  { label: "Uptime", value: "99.9%" },
  { label: "Response Time", value: "<2s" }
];

const pricingPlans = [
  {
    name: "Free",
    price: "$0",
    period: "forever",
    description: "Perfect for getting started with AI conversations",
    credits: 100,
    searches: 10,
    maxSpending: "$1.00",
    features: [
      "100 AI conversation credits",
      "10 web searches per month",
      "Access to all AI models",
      "Real-time streaming responses",
      "Conversation sharing",
      "Multi-language support"
    ],
    popular: false,
    available: true,
    buttonText: "Get Started Free"
  },
  {
    name: "Pro",
    price: "$10",
    period: "month",
    description: "For professionals who demand excellence",
    credits: 500,
    searches: 200,
    maxSpending: "$10.00",
    features: [
      "500 AI conversation credits",
      "200 web searches per month",
      "Priority model access",
      "Advanced code assistance",
      "Image generation & analysis",
      "Smart memory system",
      "Team collaboration tools (coming soon)",
      "Priority support"
    ],
    popular: true,
    available: false,
    buttonText: "Coming Soon"
  },
  {
    name: "Ultra",
    price: "$40",
    period: "month",
    description: "For visionary teams and power users",
    credits: 2500,
    searches: 1000,
    maxSpending: "$25.00",
    features: [
      "2,500 AI conversation credits",
      "1,000 web searches per month",
      "Unlimited model switching",
      "Custom AI instructions",
      "Advanced analytics (coming soon)",
      "Team management (coming soon)",
      "White-label options (coming soon)",
      "Dedicated support"
    ],
    popular: false,
    available: false,
    buttonText: "Coming Soon"
  },
  {
    name: "Max",
    price: "$200",
    period: "month",
    description: "For enterprises and power AI developers",
    credits: 20000,
    searches: 5000,
    maxSpending: "$120.00",
    features: [
      "20,000 AI conversation credits",
      "5,000 web searches per month",
      "Unlimited model access and switching",
      "Enterprise-grade code assistance",
      "Premium image generation & analysis",
      "Enhanced smart memory system",
      "Advanced team collaboration tools",
      "Custom API integrations",
      "Dedicated account manager",
      "24/7 priority support"
    ],
    popular: false,
    available: false,
    buttonText: "Contact Sales"
  }
];

const testimonials = [
  {
    name: "Dr. Evelyn Parker",
    role: "Chief Innovation Officer, SynthTech",
    content:
      "ErzenAI's contextual awareness is light-years ahead. It's like having an elite squad of senior analysts on-call 24/7.",
    rating: 5,
  },
  {
    name: "Liam Patel",
    role: "Head of AI Research, Algomind",
    content:
      "We benchmarked every major platform—nothing comes close to ErzenAI's speed, security, and breadth of models.",
    rating: 5,
  },
  {
    name: "Isabella Nguyen",
    role: "Product Lead, Nebula Labs",
    content:
      "ErzenAI unlocked entirely new creative workflows for our designers and engineers. Productivity has skyrocketed.",
    rating: 5,
  },
  {
    name: "Noah Müller",
    role: "CTO, QuantumWorks",
    content:
      "With enterprise-grade security and limitless scalability, ErzenAI is now our company-wide AI standard.",
    rating: 5,
  },
  {
    name: "Aurora Kim",
    role: "Founder & CEO, StellarStartups",
    content:
      "From ideation to launch, ErzenAI is the secret weapon driving our rapid growth and innovation culture.",
    rating: 5,
  },
];

const modelBadges = [
  "GPT-4o",
  "Claude 4.0",
  "Gemini 2.5 Pro",
  "DeepSeek R1",
  "Llama 3",
  "Mixtral 8x22B",
  "Anthropic Haiku",
  "Mistral Large",
];

const faqs = [
  {
    q: "How are conversation credits used?",
    a: "Each AI response consumes 1 credit per 750 generated tokens. Credits reset monthly based on your plan.",
  },
  {
    q: "Can I switch plans at any time?",
    a: "Absolutely—upgrade, downgrade, or cancel whenever you like. Changes take effect immediately with prorated billing.",
  },
  {
    q: "Do you store my data?",
    a: "We only keep conversation data long enough to generate a response, unless you explicitly choose to save it.",
  },
  {
    q: "Which payment methods do you accept?",
    a: "All major credit cards, Apple Pay, Google Pay, and PayPal are supported via our secure Stripe integration.",
  },
];

export function Homepage() {
  const handleGetStarted = () => {
    window.history.pushState({}, '', '/login');
    window.dispatchEvent(new PopStateEvent('popstate'));
  };

  const handleLearnMore = () => {
    document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-muted/40 overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 z-0">
        <ParticlesBackground />
      </div>
      
      {/* Gradient Overlays */}
      <div className="absolute inset-0 bg-gradient-to-tr from-primary/8 via-transparent to-accent/8 opacity-60"></div>
      
      {/* Content */}
      <div className="relative z-10">
        {/* Header */}
        <header className="sticky top-0 z-40 bg-background/70 supports-[backdrop-blur]:bg-background/60 backdrop-blur-lg border-b border-border/30 shadow-md">
          <div className="container mx-auto px-4 h-16 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <img src="/icon0.svg" alt="ErzenAI logo" className="h-9 w-9 rounded-lg shadow-md ring-1 ring-primary/40" />
              <span className="text-2xl md:text-3xl font-extrabold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent">ErzenAI</span>
            </div>
            <nav className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-muted-foreground hover:text-foreground transition-colors">Features</a>
              <a href="#pricing" className="text-muted-foreground hover:text-foreground transition-colors">Pricing</a>
              <a href="#about" className="text-muted-foreground hover:text-foreground transition-colors">About</a>
              <Button
                onClick={handleGetStarted}
                size="sm"
                className="px-5 py-2 rounded-full bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-lg hover:brightness-110 transition"
              >
                Get Started
                <ArrowRight className="h-4 w-4 ml-1" />
              </Button>
            </nav>
          </div>
        </header>

        {/* Hero Section */}
        <section className="relative pt-24 pb-32 px-4 overflow-hidden">
          {/* Background decorative blobs */}
          <div className="absolute inset-0 -z-10 pointer-events-none">
            <div
              className="absolute top-1/3 left-1/2 -translate-x-1/2 w-[700px] h-[700px] rounded-full bg-gradient-to-br from-primary/30 via-accent/40 to-transparent blur-3xl opacity-60 animate-blob"
            />
            <div
              className="absolute bottom-0 right-0 w-[500px] h-[500px] rounded-full bg-gradient-to-tr from-accent/40 via-primary/30 to-transparent blur-3xl opacity-50 animate-blob"
              style={{ animationDelay: "-8s" }}
            />
          </div>

          <div className="container mx-auto text-center">
            <Badge variant="secondary" className="mb-8 px-4 py-2 text-base">
              <Sparkles className="h-4 w-4 mr-2" />
              Powered by Advanced AI
            </Badge>

            <h1 className="text-6xl md:text-8xl lg:text-9xl font-extrabold tracking-tight mb-8 leading-tight">
              The Future of
              <span
                className="bg-[linear-gradient(110deg,_hsl(var(--primary)),_hsl(var(--accent)),_hsl(var(--primary)))] bg-clip-text text-transparent animate-gradient ml-2"
              >
                AI Conversations
              </span>
            </h1>

            <p className="text-2xl text-muted-foreground mb-12 max-w-4xl mx-auto leading-relaxed">
              Experience next-level AI interactions with lightning-fast responses, unmatched context awareness, and breathtaking design—built to elevate your creativity & productivity.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Button size="lg" onClick={handleGetStarted} className="group shadow-xl px-8 py-6 text-lg">
                Start Chatting Now
                <MessageSquare className="h-6 w-6 ml-3 group-hover:scale-110 transition-transform" />
              </Button>
              <Button variant="outline" size="lg" onClick={handleLearnMore} className="px-8 py-6 text-lg border-border/60 hover:border-border">
                Learn More
                <ChevronRight className="h-6 w-6 ml-3" />
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-12 mt-24">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-4xl md:text-5xl font-extrabold text-foreground drop-shadow-sm">
                    {stat.value}
                  </div>
                  <div className="text-sm md:text-base text-muted-foreground uppercase tracking-wide">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-20 px-4">
          <div className="container mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-5xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent mb-6 drop-shadow-sm">
                Unrivaled Capabilities for Visionaries
              </h2>
              <p className="text-2xl text-muted-foreground max-w-3xl mx-auto">
                Explore the elite toolkit that elevates ErzenAI above every other AI platform on the planet.
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {features.map((feature, index) => (
                <Card key={index} className="group relative overflow-hidden rounded-2xl border border-border/30 bg-card/80 backdrop-blur-sm p-6 transition-transform duration-300 hover:-translate-y-3 hover:shadow-2xl hover:ring-1 hover:ring-primary/40">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-lg bg-gradient-to-br from-primary/10 to-accent/10 group-hover:from-primary/20 group-hover:to-accent/20 transition-all">
                        <feature.icon className="h-6 w-6 text-primary" />
                      </div>
                      <CardTitle className="text-lg">{feature.title}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-base leading-relaxed">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* About Section */}
        <section id="about" className="py-20 px-4">
          <div className="container mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-5xl font-extrabold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent drop-shadow-sm">
                  Crafted for Tomorrow
                </h2>
                <p className="text-xl text-muted-foreground mb-8 leading-relaxed max-w-xl">
                  From cutting-edge research labs to global enterprise deployments, ErzenAI powers the next generation of ideas. Every pixel, every interaction, and every response is engineered for the innovators shaping tomorrow.
                </p>
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <Globe className="h-6 w-6 text-primary" />
                    <span className="text-lg">Global accessibility with multi-language fluency</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <Shield className="h-6 w-6 text-primary" />
                    <span className="text-lg">Enterprise-grade security & privacy</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <Zap className="h-6 w-6 text-primary" />
                    <span className="text-lg">Real-time responses under 2 seconds</span>
                  </div>
                </div>
              </div>
              <div className="relative">
                <Card className="p-8 rounded-2xl border border-border/30 bg-card/80 backdrop-blur-sm shadow-xl">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-sm text-muted-foreground">Online</span>
                      </div>
                      <Badge variant="secondary">AI Assistant</Badge>
                    </div>
                    <div className="bg-muted/50 rounded-lg p-4">
                      <p className="text-sm text-muted-foreground">Hello! How can I help you today?</p>
                    </div>
                    <div className="bg-primary/10 rounded-lg p-4 ml-8">
                      <p className="text-sm">Can you help me write a Python function?</p>
                    </div>
                    <div className="bg-muted/50 rounded-lg p-4">
                      <p className="text-sm text-muted-foreground">
                        Absolutely! I'd be happy to help you write a Python function. 
                        What would you like the function to do?
                      </p>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Model Badges Section */}
        <section className="py-20 px-4 bg-gradient-to-b from-background/70 to-background/90">
          <div className="container mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-extrabold mb-10 bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent drop-shadow-sm">
              Built on the World's Most Advanced Models
            </h2>
            <div className="flex flex-wrap justify-center gap-4 max-w-4xl mx-auto">
              {modelBadges.map((name) => (
                <span
                  key={name}
                  className="px-4 py-2 rounded-full bg-primary/10 text-sm md:text-base text-foreground ring-1 ring-primary/20 backdrop-blur-sm"
                >
                  {name}
                </span>
              ))}
              <span className="px-4 py-2 rounded-full bg-primary/10 text-sm md:text-base text-foreground ring-1 ring-primary/20 backdrop-blur-sm">
                + dozens more
              </span>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section id="testimonials" className="py-20 px-4">
          <div className="container mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-5xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent mb-6 drop-shadow-sm">
                Preferred by Global Innovators
              </h2>
              <p className="text-2xl text-muted-foreground max-w-3xl mx-auto">
                Hear how industry leaders leverage ErzenAI to redefine what's possible.
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {testimonials.map((testimonial, index) => (
                <Card
                  key={index}
                  className="group relative overflow-hidden rounded-2xl border border-border/30 bg-card/80 backdrop-blur-sm p-6 transition-transform duration-300 hover:-translate-y-3 hover:shadow-2xl hover:ring-1 hover:ring-primary/40"
                >
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-base">{testimonial.name}</CardTitle>
                        <CardDescription>{testimonial.role}</CardDescription>
                      </div>
                      <div className="flex">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400 drop-shadow" />
                        ))}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed italic">
                      "{testimonial.content}"
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="py-20 px-4">
          <div className="container mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-5xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent mb-6 drop-shadow-sm">
                Choose Your Elite Plan
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Start free and scale as you grow. All plans include access to our powerful AI models.
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {pricingPlans.map((plan, index) => (
                <Card 
                  key={index} 
                  className={`relative p-8 border-border/40 backdrop-blur-sm bg-card/80 ${
                    plan.popular ? 'ring-2 ring-primary/50 shadow-xl scale-105' : ''
                  } ${!plan.available ? 'opacity-90' : ''}`}
                >
                  {plan.popular && (
                    <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground">
                      Most Popular
                    </Badge>
                  )}
                  
                  <CardHeader className="text-center pb-8">
                    <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                    <div className="mt-4">
                      <span className="text-4xl font-bold text-foreground">{plan.price}</span>
                      <span className="text-muted-foreground">/{plan.period}</span>
                    </div>
                    <CardDescription className="text-base mt-2">
                      {plan.description}
                    </CardDescription>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      {plan.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-3">
                          <Check className="h-4 w-4 text-primary flex-shrink-0" />
                          <span className="text-sm text-muted-foreground">{feature}</span>
                        </div>
                      ))}
                    </div>
                    
                    <div className="pt-6">
                      <Button 
                        className={`w-full ${plan.popular ? 'bg-primary hover:bg-primary/90' : ''}`}
                        variant={plan.popular ? 'default' : 'outline'}
                        onClick={plan.available ? handleGetStarted : undefined}
                        disabled={!plan.available}
                      >
                        {!plan.available && <Clock className="h-4 w-4 mr-2" />}
                        {plan.buttonText}
                      </Button>
                    </div>
                    
                    {!plan.available && (
                      <p className="text-xs text-muted-foreground text-center pt-2">
                        Get notified when this plan becomes available
                      </p>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
            
            <div className="text-center mt-12">
              <p className="text-sm text-muted-foreground">
                All plans include secure conversations, multiple AI models, and real-time streaming.
                <br />
                Credits reset monthly. No hidden fees or long-term commitments.
              </p>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section id="faq" className="py-20 px-4">
          <div className="container mx-auto max-w-4xl">
            <h2 className="text-4xl md:text-5xl font-extrabold mb-10 bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent drop-shadow-sm text-center">
              Frequently Asked Questions
            </h2>
            <div className="space-y-4">
              {faqs.map((item, idx) => (
                <details
                  key={idx}
                  className="rounded-xl border border-border/30 backdrop-blur-sm bg-card/80 p-4 hover:shadow-md transition"
                >
                  <summary className="cursor-pointer list-none text-lg font-medium flex items-center justify-between">
                    {item.q}
                    <ChevronRight className="h-5 w-5 transition-transform details-open:rotate-90" />
                  </summary>
                  <p className="mt-2 text-muted-foreground leading-relaxed">{item.a}</p>
                </details>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4">
          <div className="container mx-auto">
            <Card className="text-center p-12 rounded-3xl border border-border/30 bg-gradient-to-br from-primary/10 via-card to-accent/10 backdrop-blur-sm shadow-2xl">
              <CardHeader>
                <CardTitle className="text-4xl font-bold text-foreground mb-4">
                  Embark on Your Premium Journey
                </CardTitle>
                <CardDescription className="text-xl max-w-2xl mx-auto">
                  Join thousands of users who are already experiencing the future of AI conversations.
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <Button size="lg" onClick={handleGetStarted} className="group">
                  Start Your Journey
                  <Sparkles className="h-5 w-5 ml-2 group-hover:scale-110 transition-transform" />
                </Button>
                <p className="text-sm text-muted-foreground mt-4">
                  No credit card required • Free to start • Instant access
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gradient-to-b from-transparent via-background/60 to-background/90 supports-[backdrop-blur]:bg-background/60 backdrop-blur-lg border-t border-border/30 py-16 px-4">
          <div className="container mx-auto flex flex-col md:flex-row md:items-center md:justify-between gap-8">
            {/* Branding */}
            <div className="flex items-center space-x-3">
              <img src="/icon0.svg" alt="ErzenAI" className="h-8 w-8 rounded-md ring-1 ring-primary/40 shadow" />
              <span className="text-xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent">ErzenAI</span>
            </div>

            {/* Quick Links */}
            <nav className="flex flex-wrap justify-center gap-x-6 gap-y-3 text-muted-foreground text-sm">
              <a href="#features" className="hover:text-foreground transition-colors">Features</a>
              <a href="#pricing" className="hover:text-foreground transition-colors">Pricing</a>
              <a href="#about" className="hover:text-foreground transition-colors">About</a>
              <a href="#testimonials" className="hover:text-foreground transition-colors">Testimonials</a>
              <a href="/privacy" className="hover:text-foreground transition-colors">Privacy</a>
              <a href="/terms" className="hover:text-foreground transition-colors">Terms</a>
            </nav>

            {/* Socials */}
            <div className="flex justify-center space-x-4">
              <a href="https://github.com" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-foreground transition-colors">
                <Github className="h-5 w-5" />
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-foreground transition-colors">
                <Twitter className="h-5 w-5" />
              </a>
            </div>

            {/* Copyright */}
            <p className="w-full text-center md:w-auto md:text-right text-xs text-muted-foreground">
              © 2025 ErzenAI. All rights reserved.
            </p>
          </div>
        </footer>
      </div>
    </div>
  );
} 