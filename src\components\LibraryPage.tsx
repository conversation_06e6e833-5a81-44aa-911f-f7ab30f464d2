import { useQ<PERSON>y } from "convex/react";
import { api } from "../../convex/_generated/api";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "./ui/button";
import { ArrowLeft, Download, ExternalLink, Sparkles, Upload } from "lucide-react";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

function ImageCard({ image, onImageClick }: { image: any, onImageClick: (url: string) => void }) {
    const isGenerated = 'generatedAt' in image;
    const date = new Date(isGenerated ? image.generatedAt : image.uploadedAt);

    return (
        <TooltipProvider>
            <div className="group relative aspect-square overflow-hidden rounded-2xl shadow-lg transition-all duration-300 ease-in-out hover:shadow-2xl" onClick={() => image.url && onImageClick(image.url)}>
                {image.url ? (
                    <img src={image.url} alt={image.prompt || image.fileName} className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110" />
                ) : (
                    <div className="w-full h-full bg-muted flex items-center justify-center">
                        <p className="text-xs text-muted-foreground">Image not available</p>
                    </div>
                )}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="absolute bottom-0 left-0 right-0 p-4 text-white translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                    <p className="text-sm font-semibold line-clamp-2">{image.prompt || image.fileName}</p>
                    <div className="flex items-center justify-between text-xs text-white/80 mt-2">
                        <div className="flex items-center gap-1">
                            {isGenerated ? <Sparkles size={12} /> : <Upload size={12} />}
                            <span>{isGenerated ? "Generated" : "Uploaded"}</span>
                        </div>
                        <span>{date.toLocaleDateString()}</span>
                    </div>
                </div>
                <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                     <Tooltip>
                        <TooltipTrigger asChild>
                            <Button size="icon" variant="ghost" className="h-8 w-8 text-white hover:bg-white/20 hover:text-white" onClick={(e) => { e.stopPropagation(); window.open(image.url, '_blank')}}>
                                <ExternalLink size={14} />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>Open in new tab</TooltipContent>
                    </Tooltip>
                    <Tooltip>
                         <TooltipTrigger asChild>
                            <Button size="icon" variant="ghost" className="h-8 w-8 text-white hover:bg-white/20 hover:text-white" onClick={(e) => { e.stopPropagation(); /* Download logic */ }}>
                                <Download size={14} />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>Download</TooltipContent>
                    </Tooltip>
                </div>
            </div>
        </TooltipProvider>
    );
}


export function LibraryPage() {
    const generatedImages = useQuery(api.generatedImages.getGeneratedImages);
    const uploadedImages = useQuery(api.files.getUploadedImages);

    const allImages = (generatedImages && uploadedImages)
        ? [...generatedImages, ...uploadedImages].sort((a, b) => {
            const timeA = (a as any).generatedAt ?? (a as any).uploadedAt;
            const timeB = (b as any).generatedAt ?? (b as any).uploadedAt;
            return timeB - timeA;
        })
        : null;

    const navigateBack = () => {
        window.history.pushState({}, '', '/');
        window.dispatchEvent(new PopStateEvent('popstate'));
    };

    const handleImageClick = (url: string) => {
        window.open(url, '_blank');
    };

    return (
        <div className="h-full flex flex-col bg-gradient-to-br from-background via-background/95 to-muted/20">
            <header className="p-4 border-b border-border/50 bg-gradient-to-r from-background/90 to-muted/20 backdrop-blur-sm sticky top-0 z-10 flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button variant="ghost" size="icon" onClick={navigateBack} className="hover:bg-background/80 rounded-full">
                        <ArrowLeft />
                    </Button>
                    <h1 className="text-2xl font-bold tracking-tight text-foreground">Your Library</h1>
                </div>
                 {allImages && <p className="text-sm text-muted-foreground">{allImages.length} items</p>}
            </header>
            <ScrollArea className="flex-grow">
                 {allImages === null ? (
                    <div className="flex items-center justify-center h-full p-8">
                        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
                    </div>
                 ) : allImages.length === 0 ? (
                    <div className="text-center text-muted-foreground p-16">
                        <div className="w-20 h-20 mx-auto bg-muted rounded-full flex items-center justify-center mb-4">
                            <Sparkles size={32} className="text-primary" />
                        </div>
                        <h2 className="text-xl font-semibold">Your library is empty</h2>
                        <p>Generated or uploaded images will appear here.</p>
                    </div>
                ) : (
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-6 p-6">
                        {allImages.map((image) => (
                            <ImageCard key={image._id} image={image} onImageClick={handleImageClick} />
                        ))}
                    </div>
                )}
            </ScrollArea>
        </div>
    );
} 