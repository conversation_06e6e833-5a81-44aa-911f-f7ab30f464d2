import { useEffect, useState } from "react";
import { useQuery, useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ArrowLeft, Github, Rocket, RefreshCw, AlertCircle } from "lucide-react";
import { Tooltip, TooltipProvider, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";

/**
 * ReposePage – Front-end UI for deploying AI agents on a GitHub repository.
 * Back-end functionality (repo cloning, agent execution, etc.) will be wired up later.
 */
export function ReposePage() {
  const githubLinked = useQuery(api.github.isLinked);
  const startOAuth = useAction(api.github.startOAuth);
  const fetchRepo = useAction(api.repositories_node.fetchRepo);

  const [repoUrl, setRepoUrl] = useState("https://github.com/");
  const [branch, setBranch] = useState("main");
  const [taskDescription, setTaskDescription] = useState("");

  // Placeholder for branch loading state – to be implemented with real GitHub calls later.
  const [loadingBranches, setLoadingBranches] = useState(false);
  const [branches, setBranches] = useState<string[]>([]);
  const [fetchError, setFetchError] = useState<string | null>(null);

  // Allow users to skip GitHub linking for public repositories
  const [usePublicRepo, setUsePublicRepo] = useState(false);

  // Tab state: "tasks" | "archive" | "repos"
  const [activeTab, setActiveTab] = useState<"tasks" | "archive" | "repos">("tasks");

  const repos = useQuery(api.repositories.listCachedRepos, activeTab === "repos" ? {} : "skip");

  // Parses repo URL change and (future) fetch branch list
  useEffect(() => {
    // Simple heuristic: fetch branches after user finishes typing the repo URL.
    const timeout = setTimeout(() => {
      if (!repoUrl.startsWith("https://github.com/")) return;
      const parts = repoUrl.replace("https://github.com/", "").split("/");
      if (parts.length < 2) return;
      const owner = parts[0];
      const repo = parts[1].replace(/\.git$/, "");

      // TODO: Replace with server call that fetches branches via Octokit.
      // For now, mock the common cases.
      setLoadingBranches(true);
      setFetchError(null);
      setTimeout(() => {
        setBranches(["main", "master", "develop"]);
        setLoadingBranches(false);
      }, 600);
    }, 600);
    return () => clearTimeout(timeout);
  }, [repoUrl]);

  const handleRunAgent = () => {
    fetchRepo({ repoUrl, branch })
      .then(() => {
        console.log("Repository cached, ready to code", { repoUrl, branch });
      })
      .catch(console.error);
  };

  const navigateBack = () => {
    window.history.pushState({}, "", "/");
    window.dispatchEvent(new PopStateEvent("popstate"));
  };

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-background via-background/95 to-muted/20">
      {/* Header */}
      <header className="p-4 border-b border-border/50 bg-gradient-to-r from-background/90 to-muted/20 backdrop-blur-sm sticky top-0 z-10 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={navigateBack} className="hover:bg-background/80 rounded-full">
            <ArrowLeft />
          </Button>
          <h1 className="text-2xl font-bold tracking-tight text-foreground flex items-center gap-2">
            <Rocket className="w-6 h-6 text-primary" /> What should we code next?
          </h1>
        </div>
      </header>

      {/* Content */}
      <ScrollArea className="flex-grow p-6">
        <div className="max-w-3xl mx-auto space-y-10">
          {/* Composer Card */}
          <div className="bg-background border border-border/50 rounded-2xl p-6 space-y-4 shadow-sm">
            <Textarea
              id="task-desc"
              placeholder="In my current project, find a bug in the last 5 commits and fix it"
              rows={4}
              value={taskDescription}
              onChange={(e) => setTaskDescription(e.target.value)}
            />

            <div className="flex items-center flex-wrap gap-2">
              <Input
                id="repo-url"
                placeholder="https://github.com/owner/repo"
                value={repoUrl}
                onChange={(e) => setRepoUrl(e.target.value)}
                className="flex-grow min-w-[12rem]"
              />

              {branches.length > 0 ? (
                <select
                  id="branch"
                  className="w-32 border border-border/50 rounded-lg px-3 py-2 bg-background"
                  value={branch}
                  onChange={(e) => setBranch(e.target.value)}
                >
                  {branches.map((b) => (
                    <option key={b} value={b}>
                      {b}
                    </option>
                  ))}
                </select>
              ) : (
                <Input
                  id="branch"
                  placeholder="main"
                  value={branch}
                  onChange={(e) => setBranch(e.target.value)}
                  className="w-32"
                />
              )}

              {loadingBranches && <RefreshCw className="w-4 h-4 animate-spin text-muted-foreground" />}

              {!githubLinked && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => {
                          startOAuth({
                            redirectUri: `${window.location.origin}/github/oauth/callback`,
                          })
                            .then((url) => (window.location.href = url))
                            .catch(console.error);
                        }}
                        className="shrink-0"
                      >
                        <Github className="w-4 h-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Link GitHub for private repos</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}

              <Button
                variant="secondary"
                onClick={handleRunAgent}
                disabled={!repoUrl || !branch || !taskDescription.trim()}
                className="shrink-0"
              >
                Ask
              </Button>

              <Button
                onClick={handleRunAgent}
                disabled={!repoUrl || !branch || !taskDescription.trim()}
                className="shrink-0"
              >
                Code
              </Button>
            </div>
          </div>

          {/* Tabs */}
          <div>
            <div className="flex gap-10 border-b border-border/50">
              <button
                className={`px-1 pb-2 font-medium ${
                  activeTab === "tasks" ? "border-b-2 border-primary" : "text-muted-foreground"
                }`}
                onClick={() => setActiveTab("tasks")}
              >
                Tasks
              </button>
              <button
                className={`px-1 pb-2 font-medium ${
                  activeTab === "archive" ? "border-b-2 border-primary" : "text-muted-foreground"
                }`}
                onClick={() => setActiveTab("archive")}
              >
                Archive
              </button>
              <button
                className={`px-1 pb-2 font-medium ${
                  activeTab === "repos" ? "border-b-2 border-primary" : "text-muted-foreground"
                }`}
                onClick={() => setActiveTab("repos")}
              >
                Repos
              </button>
            </div>

            {activeTab === "tasks" && (
              <div className="pt-4 text-muted-foreground text-sm">No tasks yet.</div>
            )}

            {activeTab === "archive" && (
              <div className="pt-4 text-muted-foreground text-sm">No archived tasks.</div>
            )}

            {activeTab === "repos" && (
              <div className="pt-4">
                {repos === undefined ? (
                  <div className="text-sm text-muted-foreground">Loading…</div>
                ) : repos.length === 0 ? (
                  <div className="text-sm text-muted-foreground">No cached repositories yet.</div>
                ) : (
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="text-left text-muted-foreground">
                        <th className="py-1">Repository</th>
                        <th className="py-1">Branch</th>
                        <th className="py-1">Commit</th>
                        <th className="py-1">Fetched</th>
                      </tr>
                    </thead>
                    <tbody>
                      {repos.map((r) => (
                        <tr key={`${r.repoFullName}-${r.branch}`} className="border-t border-border/50">
                          <td className="py-1 pr-4">{r.repoFullName}</td>
                          <td className="py-1 pr-4">{r.branch}</td>
                          <td className="py-1 pr-4 font-mono text-xs">{r.commitSha.slice(0, 7)}</td>
                          <td className="py-1 text-muted-foreground">
                            {new Date(r.fetchedAt).toLocaleString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            )}
          </div>
        </div>
      </ScrollArea>
    </div>
  );
} 