import React, { useEffect, useRef, useState } from "react";
import { useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";

interface VoiceCallDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSendMessage: (content: string) => void;
  isGenerating?: boolean;
}

/**
 * A Siri-style voice assistant dialog that leverages the Web Speech API for
 * in-browser speech-to-text, positioned in the top-right corner of the screen.
 */
export const VoiceCallDialog: React.FC<VoiceCallDialogProps> = ({
  open,
  onOpenChange,
  onSendMessage,
  isGenerating = false,
}) => {
  const [transcript, setTranscript] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [isReplying, setIsReplying] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const recognitionRef = useRef<any>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const micStreamRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const animationFrameRef = useRef<number | null>(null);

  // Convex action that turns text into spoken audio via Gemini Live
  const voiceReply = useAction(api.geminiLive.voiceReply);

  // Keep track of text that has already been finalized so we don't duplicate it
  const finalizedTextRef = useRef("");

  // Initialize audio setup
  const setupAudio = async () => {
    try {
      if (!isRecording) return;
      
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // Set up audio context for visualizing microphone level
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      analyserRef.current = audioContextRef.current.createAnalyser();
      micStreamRef.current = audioContextRef.current.createMediaStreamSource(stream);
      
      analyserRef.current.fftSize = 256;
      micStreamRef.current.connect(analyserRef.current);
      
      // Start analyzing audio levels
      const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
      
      const updateAudioLevel = () => {
        if (!analyserRef.current || !isRecording) return;
        
        analyserRef.current.getByteFrequencyData(dataArray);
        
        // Calculate average volume level
        let sum = 0;
        for (let i = 0; i < dataArray.length; i++) {
          sum += dataArray[i];
        }
        const avg = sum / dataArray.length;
        const normalizedLevel = Math.min(1, avg / 128); // Normalize to 0-1
        
        setAudioLevel(normalizedLevel);
        animationFrameRef.current = requestAnimationFrame(updateAudioLevel);
      };
      
      animationFrameRef.current = requestAnimationFrame(updateAudioLevel);
      
    } catch (err) {
      console.error("Failed to access microphone", err);
    }
  };

  // Helper to play Base64-encoded PCM (16-bit @ 24 kHz) returned by the backend
  const playAudio = async (base64: string) => {
    try {
      // Decode Base64 → ArrayBuffer
      const binary = atob(base64);
      const len = binary.length;
      const buffer = new Uint8Array(len);
      for (let i = 0; i < len; i++) buffer[i] = binary.charCodeAt(i);

      // Web Audio expects an AudioBuffer – re-interpret raw PCM^16 at 24 kHz
      const audioCtx = new (window.AudioContext || (window as any).webkitAudioContext)({ sampleRate: 24000 });
      const int16 = new Int16Array(buffer.buffer);
      const float32 = new Float32Array(int16.length);
      for (let i = 0; i < int16.length; i++) {
        float32[i] = int16[i] / 32768; // Convert to [-1, 1]
      }
      const audioBuffer = audioCtx.createBuffer(1, float32.length, 24000);
      audioBuffer.getChannelData(0).set(float32);

      const source = audioCtx.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(audioCtx.destination);
      source.start();
    } catch (err) {
      console.error("Failed to play audio", err);
    }
  };

  // Cleanup audio resources
  const cleanupAudio = () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }
    
    if (micStreamRef.current) {
      micStreamRef.current.disconnect();
      micStreamRef.current = null;
    }
    
    if (audioContextRef.current?.state !== 'closed') {
      void audioContextRef.current?.close();
    }
  };

  // Handle stopping recording and closing dialog
  const handleStop = () => {
    if (isRecording && recognitionRef.current) {
      recognitionRef.current.stop();
      cleanupAudio();
    }
    onOpenChange(false);
  };

  // Reset state when dialog closes
  useEffect(() => {
    if (!open) {
      if (recognitionRef.current && isRecording) {
        recognitionRef.current.stop();
      }
      setIsRecording(false);
      setTranscript("");
      finalizedTextRef.current = "";
      cleanupAudio();
    }
    
    return () => cleanupAudio();
  }, [open, isRecording]);

  useEffect(() => {
    const SpeechRecognitionImpl =
      (window as any).SpeechRecognition ||
      (window as any).webkitSpeechRecognition;
    if (!SpeechRecognitionImpl) {
      console.warn("Web Speech API is not supported by this browser.");
      return;
    }
    const recognition: any = new SpeechRecognitionImpl();
    recognition.lang = "en-US";
    recognition.interimResults = true;
    recognition.continuous = true;

    recognition.onresult = (event: any) => {
      let newFinalizedChunk = "";
      let interimTranscript = "";

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        if (result.isFinal) {
          newFinalizedChunk += result[0].transcript + " ";
        } else {
          interimTranscript += result[0].transcript;
        }
      }

      // Append the newly-finalized chunk to our cumulative finalized string
      if (newFinalizedChunk) {
        finalizedTextRef.current += newFinalizedChunk;
      }

      // Combine finalized + current interim for display
      setTranscript(finalizedTextRef.current + interimTranscript);

      // If we have a new finalized chunk, send it for audio reply
      if (newFinalizedChunk.trim()) {
        handleUtterance(newFinalizedChunk.trim());
      }
    };

    recognition.onend = () => {
      setIsRecording(false);
      cleanupAudio();
    };

    recognition.onerror = (event: any) => {
      console.error("Speech recognition error", event.error);
      setIsRecording(false);
      cleanupAudio();
    };

    recognitionRef.current = recognition;
  }, []);

  const startStopRecording = () => {
    const recognition = recognitionRef.current;
    if (!recognition) return;

    if (isRecording) {
      recognition.stop();
      cleanupAudio();
    } else {
      setTranscript("");
      finalizedTextRef.current = "";
      recognition.start();
      setIsRecording(true);
      void setupAudio();
    }
  };

  // Helper to dispatch a user utterance to audio reply only (no chat message)
  const handleUtterance = (text: string) => {
    if (!text.trim()) return;
    if (isGenerating || isReplying) return; // Skip if model busy or already replying

    // Reset transcript immediately so the next utterance starts from scratch
    setTranscript("");
    finalizedTextRef.current = "";

    setIsReplying(true);

    // Fire-and-forget audio reply (no chat bubble)
    void voiceReply({ text })
      .then((res) => {
        if (res?.audioBase64) {
          void playAudio(res.audioBase64);
        }
      })
      .catch((e) => {
        console.error("voiceReply error", e);
      })
      .finally(() => {
        setIsReplying(false);
      });
    // Do NOT send the message to chat
  };

  // Automatically handle audio when recording stops and we have a transcript
  useEffect(() => {
    if (!isRecording && transcript.trim()) {
      // Clear the transcript and reset finalized text ref when we stop recording
      setTranscript("");
      finalizedTextRef.current = "";
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isRecording]);

  // Start recording automatically when dialog opens, stop when closed
  useEffect(() => {
    if (open) {
      // small delay to ensure recognitionRef initialized
      setTimeout(() => {
        if (!isRecording) startStopRecording();
      }, 100);
    } else if (isRecording) {
      startStopRecording(); // stops recording
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  // Toggle colorful outline on the body only while voice assistant is active
  useEffect(() => {
    if (open) {
      document.body.classList.add("voice-outline");
    } else {
      document.body.classList.remove("voice-outline");
    }
    return () => {
      document.body.classList.remove("voice-outline");
    };
  }, [open]);

  // Generate colors for the Siri-style waves
  const generateWaveColors = () => {
    const colors = [
      'rgba(255, 59, 48, 0.7)',  // Red
      'rgba(255, 149, 0, 0.7)',  // Orange
      'rgba(255, 204, 0, 0.7)',  // Yellow
      'rgba(76, 217, 100, 0.7)', // Green
      'rgba(90, 200, 250, 0.7)',  // Light Blue
      'rgba(0, 122, 255, 0.7)',  // Blue
      'rgba(88, 86, 214, 0.7)',  // Purple
      'rgba(255, 45, 85, 0.7)',  // Pink
    ];
    
    return colors;
  };

  return (
    <>
      {/* Add CSS for Siri-style animations */}
      <style dangerouslySetInnerHTML={{
        __html: `
        @keyframes siriWave {
          0% { transform: scaleY(0.1); }
          50% { transform: scaleY(1); }
          100% { transform: scaleY(0.1); }
        }
        
        @keyframes siriPulse {
          0% { transform: scale(0.95); opacity: 1; }
          50% { transform: scale(1.05); opacity: 0.8; }
          100% { transform: scale(0.95); opacity: 1; }
        }
        
        @keyframes rotate {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        
        .siri-transcription {
          animation: fadeIn 0.3s forwards;
        }
        
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        `
      }} />
      
      {/* Directly render the orb without Dialog component to avoid any blur effects */}
      {open && (
        <>
          {/* Full-screen transparent overlay to capture clicks anywhere */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={handleStop}
          />
          
          <div className="fixed top-6 right-6 z-50 flex flex-col items-end pointer-events-auto">
            {/* Transcript bubble */}
            {transcript && (
              <div 
                className="siri-transcription mb-3 max-w-[250px] bg-black/75 text-white px-4 py-2 rounded-2xl text-sm shadow-lg"
                onClick={(e) => e.stopPropagation()}
              >
                {transcript}
              </div>
            )}
            
            {/* Siri orb */}
            <div className="relative" onClick={(e) => e.stopPropagation()}>
              {/* Colorful waves that appear during recording */}
              {isRecording && (
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-14 h-14 -z-10">
                  {generateWaveColors().map((color, i) => (
                    <div 
                      key={i}
                      className="absolute inset-0 rounded-full"
                      style={{
                        backgroundColor: color,
                        transform: `scale(${1 + (i * 0.15) + (audioLevel * 0.3)})`,
                        opacity: Math.max(0.1, Math.min(0.7, audioLevel * (1 - (i * 0.1)))),
                        animation: isRecording ? `siriWave ${0.8 + (i * 0.1)}s infinite alternate ease-in-out` : 'none',
                        animationDelay: `${i * 0.1}s`
                      }}
                    />
                  ))}
                </div>
              )}
              
              {/* Main orb */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  startStopRecording();
                  if (isRecording) {
                    onOpenChange(false);
                  }
                }}
                disabled={isGenerating}
                className={cn(
                  "relative flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r shadow-lg transition-all duration-200",
                  isRecording
                    ? "from-blue-500 to-purple-600 shadow-blue-500/30"
                    : isReplying
                    ? "from-indigo-500 to-purple-700 animate-pulse"
                    : "from-slate-700 to-slate-900 shadow-black/20"
                )}
                style={{
                  animation: isRecording ? 'siriPulse 2s infinite' : 'none'
                }}
              >
                {/* Wave lines inside the orb */}
                <div className={cn(
                  "flex items-center justify-center space-x-0.5 h-5",
                  isRecording ? "opacity-100" : "opacity-70"
                )}>
                  {Array(5).fill(0).map((_, i) => (
                    <div
                      key={i}
                      className="w-0.5 rounded-full bg-white"
                      style={{
                        height: isRecording 
                          ? `${30 + (Math.sin((i / 4) * Math.PI) * 40) + (audioLevel * 50)}%`
                          : '40%',
                        animation: isRecording 
                          ? `siriWave ${0.5 + (i * 0.1)}s infinite alternate ease-in-out` 
                          : 'none'
                      }}
                    />
                  ))}
                </div>

                {/* Loader shown while waiting for Gemini Live */}
                {isReplying && !isRecording && (
                  <Loader2 className="absolute h-5 w-5 text-white animate-spin" />
                )}
              </button>
            </div>
          </div>
        </>
      )}
    </>
  );
}; 