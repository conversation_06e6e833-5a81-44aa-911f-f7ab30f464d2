import { useState } from "react";
import { useQ<PERSON>y, useMutation } from "convex/react";
import { toast } from "sonner";
import { api } from "../../../convex/_generated/api";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Eye,
  EyeOff,
  Trash2,
  Zap,
  Infinity as InfinityIcon,
  Lock,
} from "lucide-react";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { Doc } from "../../../convex/_generated/dataModel";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { PROVIDER_CONFIGS } from "@/lib/models";

type ApiKeyProvider = Doc<"apiKeys">["provider"];

// API key specific configurations for providers that need special handling
const API_KEY_CONFIGS = {
  openai: { keyPlaceholder: "sk-...", description: "GPT models from OpenAI" },
  google: { keyPlaceholder: "AIza...", description: "Gemini models from Google" },
  anthropic: { keyPlaceholder: "sk-ant-...", description: "Claude models from Anthropic" },
  openrouter: { keyPlaceholder: "sk-or-...", description: "Access to multiple AI models" },
  groq: { keyPlaceholder: "gsk_...", description: "Ultra-fast inference" },
  deepseek: { keyPlaceholder: "sk-...", description: "Reasoning and coding models" },
  grok: { keyPlaceholder: "xai-...", description: "Elon's AI with real-time data" },
  cohere: { keyPlaceholder: "co_...", description: "Enterprise-grade language models" },
  mistral: { keyPlaceholder: "...", description: "European AI models" },
  cerebras: { keyPlaceholder: "csk-...", description: "Ultra-fast inference with Cerebras" },
  github: { keyPlaceholder: "ghp_... or github_pat_...", description: "GitHub Models inference API with PAT token" },
  tavily: { keyPlaceholder: "tvly-...", description: "Real-time web search API" },
  openweather: { keyPlaceholder: "...", description: "Weather data API" },
  firecrawl: { keyPlaceholder: "fc-...", description: "AI-ready web scraping and crawling" },
};

export function ApiKeysSection() {
  const apiKeyInfo = useQuery(api.apiKeys.getApiKeyInfo) || [];
  const upsertApiKey = useMutation(api.apiKeys.upsert);
  const removeApiKey = useMutation(api.apiKeys.remove);

  const [apiKeys, setApiKeys] = useState<Record<string, string>>({});
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({});

  const handleSave = async (provider: ApiKeyProvider, key: string) => {
    if (!key) {
      toast.error("API key cannot be empty.");
      return;
    }
    toast.promise(
      upsertApiKey({
        provider,
        apiKey: key,
      }),
      {
        loading: "Saving API key...",
        success: "API key saved!",
        error: "Failed to save API key.",
      },
    );
  };

  const handleRemove = async (provider: ApiKeyProvider) => {
    toast.promise(
      removeApiKey({ provider }),
      {
        loading: "Removing API key...",
        success: "API key removed!",
        error: "Failed to remove API key.",
      },
    );
  };

  return (
    <div className="space-y-6">
      <Alert className="shadow-sm rounded-lg">
        <Zap className="h-4 w-4" />
        <AlertTitle>Built-in vs. Your Own Keys</AlertTitle>
        <AlertDescription className="space-y-2">
          <p>
            <strong>Built-in Keys:</strong> Free usage with monthly limits
            (messages & searches count)
          </p>
          <p>
            <strong>Your Keys:</strong> {" "}
            <InfinityIcon className="inline w-5 h-5" /> Unlimited usage - no
            limits or counting!
          </p>
        </AlertDescription>
      </Alert>
      <Alert className="shadow-sm rounded-lg">
        <Lock className="h-4 w-4" />
        <AlertTitle>🔒 Encryption & Security</AlertTitle>
        <AlertDescription>
          <p>
            <strong>All API keys are encrypted</strong> before being stored in
            the database. Your keys are protected with industry-standard
            encryption.
          </p>
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {Object.entries(PROVIDER_CONFIGS).map(([provider, config]) => {
          const keyInfo = apiKeyInfo.find(
            (info) => info.provider === provider,
          );
          const hasUserKey = keyInfo?.hasUserKey || false;

          return (
            <Card key={provider} className="shadow-md border border-border/20 rounded-xl hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold">{config.name}</CardTitle>
                  <Badge variant={hasUserKey ? "default" : "secondary"} className="text-xs">
                    {hasUserKey ? "Custom Key" : "Built-in"}
                  </Badge>
                </div>
                <CardDescription className="text-sm mt-1">
                  {API_KEY_CONFIGS[provider as keyof typeof API_KEY_CONFIGS]?.description || "AI provider"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 p-6 pt-0">
                <Separator className="my-2" />
                <div className="flex items-center gap-2">
                  <div className="relative flex-1">
                    <Input
                      type={showKeys[provider] ? "text" : "password"}
                      placeholder={API_KEY_CONFIGS[provider as keyof typeof API_KEY_CONFIGS]?.keyPlaceholder || "Enter API key..."}
                      value={apiKeys[provider] ?? ""}
                      onChange={(e) =>
                        setApiKeys({ ...apiKeys, [provider]: e.target.value })
                      }
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() =>
                        setShowKeys({ ...showKeys, [provider]: !showKeys[provider] })
                      }
                    >
                      {showKeys[provider] ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      )}
                    </Button>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button
                    onClick={() =>
                      void handleSave(provider as ApiKeyProvider, apiKeys[provider] ?? "")
                    }
                    disabled={!apiKeys[provider]?.trim()}
                    className="flex-1 sm:flex-none text-sm py-2 px-4"
                  >
                    Save Key
                  </Button>
                  {hasUserKey && (
                    <Button
                      variant="destructive"
                      onClick={() => void handleRemove(provider as ApiKeyProvider)}
                      className="flex-1 sm:flex-none text-sm py-2 px-4"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Remove
                    </Button>
                  )}
                </div>
                {hasUserKey && keyInfo?.keyPreview && (
                  <p className="text-sm text-muted-foreground mt-2">
                    Key: {keyInfo.keyPreview} • Added{" "}
                    {keyInfo.addedAt
                      ? new Date(keyInfo.addedAt).toLocaleDateString()
                      : "Recently"}
                  </p>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
} 