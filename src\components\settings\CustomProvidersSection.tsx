import { useState } from "react";
import { useQuery, useMutation, useAction } from "convex/react";
import { toast } from "sonner";
import { api } from "../../../convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Plus,
  Edit,
  Trash2,
  TestTube,
  Eye,
  EyeOff,
  ExternalLink,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface CustomProvider {
  _id: string;
  name: string;
  displayName: string;
  baseURL: string;
  models: string[];
  icon?: string;
  description?: string;
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
}

interface ProviderFormData {
  name: string;
  displayName: string;
  baseURL: string;
  apiKey: string;
  models: string;
  icon: string;
  description: string;
}

export function CustomProvidersSection() {
  const customProviders = useQuery(api.customProviders.list) || [];
  const createProvider = useMutation(api.customProviders.create);
  const updateProvider = useMutation(api.customProviders.update);
  const removeProvider = useMutation(api.customProviders.remove);
  const testConnection = useAction(api.customProviders.testConnection);

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingProvider, setEditingProvider] = useState<CustomProvider | null>(null);
  const [showApiKey, setShowApiKey] = useState<Record<string, boolean>>({});
  const [formData, setFormData] = useState<ProviderFormData>({
    name: "",
    displayName: "",
    baseURL: "",
    apiKey: "",
    models: "",
    icon: "🤖",
    description: "",
  });

  const resetForm = () => {
    setFormData({
      name: "",
      displayName: "",
      baseURL: "",
      apiKey: "",
      models: "",
      icon: "🤖",
      description: "",
    });
  };

  const handleCreate = async () => {
    if (!formData.name || !formData.displayName || !formData.baseURL || !formData.apiKey) {
      toast.error("Please fill in all required fields.");
      return;
    }

    const models = formData.models
      .split("\n")
      .map((m) => m.trim())
      .filter((m) => m.length > 0);

    if (models.length === 0) {
      toast.error("Please add at least one model.");
      return;
    }

    toast.promise(
      createProvider({
        name: formData.name,
        displayName: formData.displayName,
        baseURL: formData.baseURL,
        apiKey: formData.apiKey,
        models,
        icon: formData.icon,
        description: formData.description,
      }),
      {
        loading: "Creating custom provider...",
        success: "Custom provider created!",
        error: "Failed to create custom provider.",
      }
    );

    setIsCreateDialogOpen(false);
    resetForm();
  };

  const handleUpdate = async () => {
    if (!editingProvider) return;

    const models = formData.models
      .split("\n")
      .map((m) => m.trim())
      .filter((m) => m.length > 0);

    toast.promise(
      updateProvider({
        providerId: editingProvider._id as any,
        displayName: formData.displayName,
        baseURL: formData.baseURL,
        apiKey: formData.apiKey,
        models,
        icon: formData.icon,
        description: formData.description,
      }),
      {
        loading: "Updating custom provider...",
        success: "Custom provider updated!",
        error: "Failed to update custom provider.",
      }
    );

    setEditingProvider(null);
    resetForm();
  };

  const handleRemove = async (providerId: string) => {
    toast.promise(
      removeProvider({ providerId: providerId as any }),
      {
        loading: "Removing custom provider...",
        success: "Custom provider removed!",
        error: "Failed to remove custom provider.",
      }
    );
  };

  const handleTestConnection = async () => {
    if (!formData.baseURL || !formData.apiKey) {
      toast.error("Please enter base URL and API key to test connection.");
      return;
    }

    toast.promise(
      testConnection({
        baseURL: formData.baseURL,
        apiKey: formData.apiKey,
      }),
      {
        loading: "Testing connection...",
        success: (result) => 
          result.success ? "Connection successful!" : `Connection failed: ${result.error}`,
        error: "Failed to test connection.",
      }
    );
  };

  const openEditDialog = (provider: CustomProvider) => {
    setEditingProvider(provider);
    setFormData({
      name: provider.name,
      displayName: provider.displayName,
      baseURL: provider.baseURL,
      apiKey: "", // Don't pre-fill API key for security
      models: provider.models.join("\n"),
      icon: provider.icon || "🤖",
      description: provider.description || "",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Custom Providers</h3>
          <p className="text-sm text-muted-foreground">
            Add your own OpenAI-compatible AI providers
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Add Provider
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add Custom Provider</DialogTitle>
              <DialogDescription>
                Add a new OpenAI-compatible AI provider with your API key and models.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Provider Name *</Label>
                  <Input
                    id="name"
                    placeholder="e.g., my-provider"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="displayName">Display Name *</Label>
                  <Input
                    id="displayName"
                    placeholder="e.g., My Custom Provider"
                    value={formData.displayName}
                    onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="baseURL">Base URL *</Label>
                <Input
                  id="baseURL"
                  placeholder="https://api.provider.com/v1"
                  value={formData.baseURL}
                  onChange={(e) => setFormData({ ...formData, baseURL: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="apiKey">API Key *</Label>
                <div className="relative">
                  <Input
                    id="apiKey"
                    type={showApiKey.create ? "text" : "password"}
                    placeholder="Your API key"
                    value={formData.apiKey}
                    onChange={(e) => setFormData({ ...formData, apiKey: e.target.value })}
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() =>
                      setShowApiKey({ ...showApiKey, create: !showApiKey.create })
                    }
                  >
                    {showApiKey.create ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                  </Button>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="models">Models (one per line) *</Label>
                <Textarea
                  id="models"
                  placeholder="gpt-4&#10;gpt-3.5-turbo&#10;claude-3-sonnet"
                  value={formData.models}
                  onChange={(e) => setFormData({ ...formData, models: e.target.value })}
                  rows={4}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="icon">Icon</Label>
                  <Input
                    id="icon"
                    placeholder="🤖"
                    value={formData.icon}
                    onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Input
                    id="description"
                    placeholder="Custom AI provider"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => void handleTestConnection()}>
                <TestTube className="h-4 w-4 mr-2" />
                Test Connection
              </Button>
              <Button onClick={() => void handleCreate()}>Create Provider</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {customProviders.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-center space-y-2">
              <h4 className="text-lg font-medium">No custom providers</h4>
              <p className="text-sm text-muted-foreground">
                Add your first custom OpenAI-compatible provider to get started.
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {customProviders.map((provider) => (
            <Card key={provider._id} className="shadow-md border border-border/20 rounded-xl hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold flex items-center gap-2">
                    <span>{provider.icon}</span>
                    {provider.displayName}
                  </CardTitle>
                  <Badge variant="default" className="text-xs">
                    Custom
                  </Badge>
                </div>
                <CardDescription className="text-sm mt-1">
                  {provider.description || "Custom OpenAI-compatible provider"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 p-6 pt-0">
                <Separator className="my-2" />
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <ExternalLink className="h-4 w-4" />
                    <span className="font-medium">Base URL:</span>
                    <span className="text-muted-foreground">{provider.baseURL}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <span className="font-medium">Models:</span>
                    <span className="text-muted-foreground">
                      {provider.models.length} model{provider.models.length !== 1 ? 's' : ''}
                    </span>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEditDialog(provider)}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>Edit Custom Provider</DialogTitle>
                        <DialogDescription>
                          Update your custom provider configuration.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="edit-name">Provider Name *</Label>
                            <Input
                              id="edit-name"
                              placeholder="e.g., my-provider"
                              value={formData.name}
                              disabled
                              className="bg-muted"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="edit-displayName">Display Name *</Label>
                            <Input
                              id="edit-displayName"
                              placeholder="e.g., My Custom Provider"
                              value={formData.displayName}
                              onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
                            />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="edit-baseURL">Base URL *</Label>
                          <Input
                            id="edit-baseURL"
                            placeholder="https://api.provider.com/v1"
                            value={formData.baseURL}
                            onChange={(e) => setFormData({ ...formData, baseURL: e.target.value })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="edit-apiKey">API Key *</Label>
                          <div className="relative">
                            <Input
                              id="edit-apiKey"
                              type={showApiKey.edit ? "text" : "password"}
                              placeholder="Your API key"
                              value={formData.apiKey}
                              onChange={(e) => setFormData({ ...formData, apiKey: e.target.value })}
                              className="pr-10"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                              onClick={() =>
                                setShowApiKey({ ...showApiKey, edit: !showApiKey.edit })
                              }
                            >
                              {showApiKey.edit ? (
                                <EyeOff className="h-4 w-4 text-muted-foreground" />
                              ) : (
                                <Eye className="h-4 w-4 text-muted-foreground" />
                              )}
                            </Button>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="edit-models">Models (one per line) *</Label>
                          <Textarea
                            id="edit-models"
                            placeholder="gpt-4&#10;gpt-3.5-turbo&#10;claude-3-sonnet"
                            value={formData.models}
                            onChange={(e) => setFormData({ ...formData, models: e.target.value })}
                            rows={4}
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="edit-icon">Icon</Label>
                            <Input
                              id="edit-icon"
                              placeholder="🤖"
                              value={formData.icon}
                              onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="edit-description">Description</Label>
                            <Input
                              id="edit-description"
                              placeholder="Custom AI provider"
                              value={formData.description}
                              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                            />
                          </div>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => void handleTestConnection()}>
                          <TestTube className="h-4 w-4 mr-2" />
                          Test Connection
                        </Button>
                        <Button onClick={() => void handleUpdate()}>Update Provider</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Remove
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Remove Custom Provider</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to remove "{provider.displayName}"? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => void handleRemove(provider._id)}>
                          Remove
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
