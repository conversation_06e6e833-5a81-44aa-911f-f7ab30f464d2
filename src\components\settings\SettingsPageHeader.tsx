import { ArrowLef<PERSON>, Setting<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { SignOutButton } from "@/SignOutButton";

interface SettingsPageHeaderProps {
  onBack: () => void;
}

export function SettingsPageHeader({ onBack }: SettingsPageHeaderProps) {
  return (
    <header className="border-b border-border/40 backdrop-blur-sm bg-background/80 sticky top-0 z-20 shadow-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 h-14 sm:h-16 flex items-center justify-between">
        <div className="flex items-center gap-4 min-w-0 flex-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={onBack}
            className="rounded-full h-10 w-10 hover:bg-primary/10 transition-all duration-200 flex-shrink-0"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center space-x-3 min-w-0">
            <div className="p-2 bg-primary/10 rounded-xl flex-shrink-0 transition-colors duration-200 hover:bg-primary/20">
              <Settings className="h-6 w-6 text-primary" />
            </div>
            <div className="min-w-0">
              <h1 className="text-xl font-bold text-foreground truncate">Settings</h1>
              <p className="text-sm text-muted-foreground hidden sm:block">Customize your ErzenAI experience</p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-3 flex-shrink-0">
          <SignOutButton />
        </div>
      </div>
    </header>
  );
} 